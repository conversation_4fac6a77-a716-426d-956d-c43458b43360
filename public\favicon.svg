<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
  <defs>
    <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8b5cf6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="8" cy="8" r="7.5" fill="url(#grad)" stroke="#1e293b" stroke-width="1"/>
  
  <!-- API mesh pattern -->
  <g fill="white" stroke="white" stroke-width="0.3" opacity="0.9">
    <!-- Central node -->
    <circle cx="8" cy="8" r="1" fill="white"/>
    
    <!-- Connection lines -->
    <line x1="8" y1="8" x2="4" y2="5" stroke-width="0.5"/>
    <line x1="8" y1="8" x2="12" y2="5" stroke-width="0.5"/>
    <line x1="8" y1="8" x2="4" y2="11" stroke-width="0.5"/>
    <line x1="8" y1="8" x2="12" y2="11" stroke-width="0.5"/>
    
    <!-- Outer nodes -->
    <circle cx="4" cy="5" r="0.8" fill="white"/>
    <circle cx="12" cy="5" r="0.8" fill="white"/>
    <circle cx="4" cy="11" r="0.8" fill="white"/>
    <circle cx="12" cy="11" r="0.8" fill="white"/>
    
    <!-- Pulse effect ring -->
    <circle cx="8" cy="8" r="3" fill="none" stroke="white" stroke-width="0.5" opacity="0.5"/>
  </g>
</svg>
