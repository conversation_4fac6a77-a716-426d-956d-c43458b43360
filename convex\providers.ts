import { v } from 'convex/values';
import { query, mutation } from './_generated/server';
import { getAuthUserId } from '@convex-dev/auth/server';

// Get all providers for the current user
export const getProviders = query({
	args: {},
	handler: async (ctx) => {
		const userId = await getAuthUserId(ctx);
		if (!userId) {
			throw new Error('Not authenticated');
		}

		return await ctx.db
			.query('providers')
			.withIndex('by_created_by', (q) => q.eq('createdBy', userId))
			.collect();
	},
});

// Get a specific provider by ID
export const getProvider = query({
	args: { id: v.id('providers') },
	handler: async (ctx, args) => {
		const userId = await getAuthUserId(ctx);
		if (!userId) {
			throw new Error('Not authenticated');
		}

		const provider = await ctx.db.get(args.id);
		if (!provider || provider.createdBy !== userId) {
			throw new Error('Provider not found or access denied');
		}

		return provider;
	},
});

// Create a new provider
export const createProvider = mutation({
	args: {
		name: v.string(),
		type: v.union(
			v.literal('payment'),
			v.literal('sms'),
			v.literal('email'),
			v.literal('maps'),
			v.literal('ai'),
			v.literal('storage'),
			v.literal('auth'),
			v.literal('video'),
			v.literal('realtime'),
			v.literal('analytics'),
			v.literal('monitoring')
		),
		endpoint: v.string(),
		priority: v.number(),
		isPrimary: v.optional(v.boolean()),
		configuration: v.optional(
			v.object({
				timeout: v.optional(v.number()),
				retries: v.optional(v.number()),
				headers: v.optional(v.any()),
			})
		),
	},
	handler: async (ctx, args) => {
		const userId = await getAuthUserId(ctx);
		if (!userId) {
			throw new Error('Not authenticated');
		}

		const now = Date.now();

		return await ctx.db.insert('providers', {
			...args,
			createdBy: userId,
			isHealthy: true,
			latency: 0,
			errorRate: 0,
			lastCheck: now,
			createdAt: now,
			updatedAt: now,
		});
	},
});

// Update a provider
export const updateProvider = mutation({
	args: {
		id: v.id('providers'),
		name: v.optional(v.string()),
		endpoint: v.optional(v.string()),
		priority: v.optional(v.number()),
		isPrimary: v.optional(v.boolean()),
		configuration: v.optional(
			v.object({
				timeout: v.optional(v.number()),
				retries: v.optional(v.number()),
				headers: v.optional(v.any()),
			})
		),
	},
	handler: async (ctx, args) => {
		const userId = await getAuthUserId(ctx);
		if (!userId) {
			throw new Error('Not authenticated');
		}

		const { id, ...updates } = args;
		const provider = await ctx.db.get(id);

		if (!provider || provider.createdBy !== userId) {
			throw new Error('Provider not found or access denied');
		}

		await ctx.db.patch(id, {
			...updates,
			updatedAt: Date.now(),
		});
	},
});

// Delete a provider
export const deleteProvider = mutation({
	args: { id: v.id('providers') },
	handler: async (ctx, args) => {
		const userId = await getAuthUserId(ctx);
		if (!userId) {
			throw new Error('Not authenticated');
		}

		const provider = await ctx.db.get(args.id);
		if (!provider || provider.createdBy !== userId) {
			throw new Error('Provider not found or access denied');
		}

		await ctx.db.delete(args.id);
	},
});

// Update provider health status
export const updateProviderHealth = mutation({
	args: {
		id: v.id('providers'),
		isHealthy: v.boolean(),
		latency: v.number(),
		errorRate: v.number(),
	},
	handler: async (ctx, args) => {
		const { id, ...healthData } = args;

		await ctx.db.patch(id, {
			...healthData,
			lastCheck: Date.now(),
			updatedAt: Date.now(),
		});
	},
});
import { mutation, query } from './_generated/server';
import { v } from 'convex/values';
import { auth } from './auth';

// Get all providers for the current user
export const getProviders = query({
	args: {},
	handler: async (ctx) => {
		const userId = await auth.getUserId(ctx);
		if (!userId) {
			throw new Error('Not authenticated');
		}

		const providers = await ctx.db
			.query('providers')
			.withIndex('by_user', (q) => q.eq('userId', userId))
			.collect();

		return providers;
	},
});

// Get providers by type
export const getProvidersByType = query({
	args: {
		type: v.union(
			v.literal('payment'),
			v.literal('sms'),
			v.literal('email'),
			v.literal('maps'),
			v.literal('ai'),
			v.literal('storage'),
			v.literal('auth'),
			v.literal('video'),
			v.literal('realtime'),
			v.literal('analytics'),
			v.literal('monitoring')
		),
	},
	handler: async (ctx, args) => {
		const userId = await auth.getUserId(ctx);
		if (!userId) {
			throw new Error('Not authenticated');
		}

		const providers = await ctx.db
			.query('providers')
			.withIndex('by_type', (q) => q.eq('type', args.type))
			.filter((q) => q.eq(q.field('userId'), userId))
			.collect();

		return providers.sort((a, b) => a.priority - b.priority);
	},
});

// Get a single provider
export const getProvider = query({
	args: { providerId: v.id('providers') },
	handler: async (ctx, args) => {
		const userId = await auth.getUserId(ctx);
		if (!userId) {
			throw new Error('Not authenticated');
		}

		const provider = await ctx.db.get(args.providerId);
		if (!provider) {
			throw new Error('Provider not found');
		}

		// Check ownership
		if (provider.userId !== userId) {
			throw new Error('Access denied');
		}

		return provider;
	},
});

// Add a new provider
export const addProvider = mutation({
	args: {
		name: v.string(),
		type: v.union(
			v.literal('payment'),
			v.literal('sms'),
			v.literal('email'),
			v.literal('maps'),
			v.literal('ai'),
			v.literal('storage'),
			v.literal('auth'),
			v.literal('video'),
			v.literal('realtime'),
			v.literal('analytics'),
			v.literal('monitoring')
		),
		endpoint: v.string(),
		priority: v.optional(v.number()),
		isPrimary: v.optional(v.boolean()),
		apiKey: v.optional(v.string()),
		secretKey: v.optional(v.string()),
		timeout: v.optional(v.number()),
		retries: v.optional(v.number()),
		circuitBreakerThreshold: v.optional(v.number()),
		circuitBreakerTimeout: v.optional(v.number()),
	},
	handler: async (ctx, args) => {
		const userId = await auth.getUserId(ctx);
		if (!userId) {
			throw new Error('Not authenticated');
		}

		// Get user settings for defaults
		const userSettings = await ctx.db
			.query('userSettings')
			.withIndex('by_user', (q) => q.eq('userId', userId))
			.unique();

		const now = Date.now();
		const providerId = await ctx.db.insert('providers', {
			name: args.name,
			type: args.type,
			endpoint: args.endpoint,
			isHealthy: true, // Start as healthy
			latency: 0,
			errorRate: 0,
			priority: args.priority ?? 1,
			isPrimary: args.isPrimary ?? false,
			apiKey: args.apiKey,
			secretKey: args.secretKey,
			timeout: args.timeout ?? userSettings?.defaultTimeout ?? 30000,
			retries: args.retries ?? userSettings?.defaultRetries ?? 3,
			circuitBreakerThreshold: args.circuitBreakerThreshold ?? 5,
			circuitBreakerTimeout: args.circuitBreakerTimeout ?? 60000,
			userId,
			createdAt: now,
			updatedAt: now,
		});

		// Initialize circuit breaker
		await ctx.db.insert('circuitBreakers', {
			providerId,
			state: 'CLOSED',
			failureCount: 0,
			threshold: args.circuitBreakerThreshold ?? 5,
			timeout: args.circuitBreakerTimeout ?? 60000,
			resetTimeout: 300000, // 5 minutes
			updatedAt: now,
		});

		return providerId;
	},
});

// Update provider
export const updateProvider = mutation({
	args: {
		providerId: v.id('providers'),
		name: v.optional(v.string()),
		endpoint: v.optional(v.string()),
		priority: v.optional(v.number()),
		isPrimary: v.optional(v.boolean()),
		apiKey: v.optional(v.string()),
		secretKey: v.optional(v.string()),
		timeout: v.optional(v.number()),
		retries: v.optional(v.number()),
		circuitBreakerThreshold: v.optional(v.number()),
		circuitBreakerTimeout: v.optional(v.number()),
	},
	handler: async (ctx, args) => {
		const userId = await auth.getUserId(ctx);
		if (!userId) {
			throw new Error('Not authenticated');
		}

		const provider = await ctx.db.get(args.providerId);
		if (!provider) {
			throw new Error('Provider not found');
		}

		// Check ownership
		if (provider.userId !== userId) {
			throw new Error('Access denied');
		}

		// Update provider
		const updateData: any = { ...args };
		delete updateData.providerId; // Remove from update object
		updateData.updatedAt = Date.now();

		await ctx.db.patch(args.providerId, updateData);

		// Update circuit breaker settings if changed
		if (args.circuitBreakerThreshold || args.circuitBreakerTimeout) {
			const circuitBreaker = await ctx.db
				.query('circuitBreakers')
				.withIndex('by_provider', (q) => q.eq('providerId', args.providerId))
				.unique();

			if (circuitBreaker) {
				await ctx.db.patch(circuitBreaker._id, {
					threshold: args.circuitBreakerThreshold ?? circuitBreaker.threshold,
					timeout: args.circuitBreakerTimeout ?? circuitBreaker.timeout,
					updatedAt: Date.now(),
				});
			}
		}
	},
});

// Delete provider
export const deleteProvider = mutation({
	args: { providerId: v.id('providers') },
	handler: async (ctx, args) => {
		const userId = await auth.getUserId(ctx);
		if (!userId) {
			throw new Error('Not authenticated');
		}

		const provider = await ctx.db.get(args.providerId);
		if (!provider) {
			throw new Error('Provider not found');
		}

		// Check ownership
		if (provider.userId !== userId) {
			throw new Error('Access denied');
		}

		// Delete associated circuit breaker
		const circuitBreaker = await ctx.db
			.query('circuitBreakers')
			.withIndex('by_provider', (q) => q.eq('providerId', args.providerId))
			.unique();

		if (circuitBreaker) {
			await ctx.db.delete(circuitBreaker._id);
		}

		// Delete the provider
		await ctx.db.delete(args.providerId);
	},
});

// Update provider health status
export const updateProviderHealth = mutation({
	args: {
		providerId: v.id('providers'),
		isHealthy: v.boolean(),
		latency: v.number(),
		errorRate: v.number(),
	},
	handler: async (ctx, args) => {
		const userId = await auth.getUserId(ctx);
		if (!userId) {
			throw new Error('Not authenticated');
		}

		const provider = await ctx.db.get(args.providerId);
		if (!provider || provider.userId !== userId) {
			throw new Error('Provider not found or access denied');
		}

		await ctx.db.patch(args.providerId, {
			isHealthy: args.isHealthy,
			latency: args.latency,
			errorRate: args.errorRate,
			lastCheck: new Date().toISOString(),
			updatedAt: Date.now(),
		});

		// Create health check record
		await ctx.db.insert('healthChecks', {
			providerId: args.providerId,
			status: args.isHealthy
				? 'healthy'
				: args.errorRate > 10
					? 'down'
					: 'degraded',
			latency: args.latency,
			errorRate: args.errorRate,
			timestamp: Date.now(),
		});
	},
});
