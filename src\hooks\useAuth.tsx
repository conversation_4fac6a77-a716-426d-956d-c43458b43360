import { useAuthActions } from "@convex-dev/auth/react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";

export const useAuth = () => {
  const { signIn, signOut } = useAuthActions();
  const currentUser = useQuery(api.users.getCurrentUser);
  const userSettings = useQuery(api.users.getUserSettings);
  const ensureSettings = useMutation(api.users.ensureUserSettings);

  const signInWithPassword = async (email: string, password: string, name?: string) => {
    try {
      await signIn("password", { email, password, name });
    } catch (error) {
      console.error("Sign in error:", error);
      throw error;
    }
  };

  const signUpWithPassword = async (email: string, password: string, name: string) => {
    try {
      await signIn("password", { email, password, name, flow: "signUp" });
    } catch (error) {
      console.error("Sign up error:", error);
      throw error;
    }
  };

  const logout = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error("Sign out error:", error);
      throw error;
    }
  };

  return {
    currentUser,
    userSettings,
    signInWithPassword,
    signUpWithPassword,
    logout,
    ensureSettings,
    isAuthenticated: !!currentUser,
    isLoading: currentUser === undefined,
  };
};
