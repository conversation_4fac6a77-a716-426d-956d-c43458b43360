import { useState } from "react";
import { Link, useLocation } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Activity, Shield, Settings, BarChart3, Bell, User, Menu, X, LogOut } from "lucide-react";
import { useAuth } from "@/hooks/useAuth";
const Navigation = () => {
  const [isOpen, setIsOpen] = useState(false);
  const location = useLocation();
  const { currentUser, logout } = useAuth();
  const navItems = [{
    icon: Activity,
    label: "Dashboard",
    href: "/dashboard"
  }, {
    icon: Shield,
    label: "Providers",
    href: "/providers"
  }, {
    icon: BarChart3,
    label: "Analytics",
    href: "/analytics"
  }, {
    icon: Bell,
    label: "Alerts",
    href: "/alerts"
  }, {
    icon: Settings,
    label: "Settings",
    href: "/settings"
  }];
  return <nav className="bg-card border-b border-border">
    <div className="container mx-auto px-4">
      <div className="flex items-center justify-between h-16">
        {/* Logo */}
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-cyber rounded-lg flex items-center justify-center shadow-glow">
            <Shield className="w-6 h-6 text-foreground" />
          </div>
          <div>
            <h1 className="text-xl font-bold text-foreground">PulseMesh</h1>
            <p className="text-xs text-muted-foreground">Intelligent Middleware Platform</p>
          </div>
        </div>

        {/* Desktop Navigation */}
        <div className="hidden md:flex items-center space-x-6">
          {navItems.map(item => <Link key={item.label} to={item.href} className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors ${location.pathname === item.href ? "bg-primary text-primary-foreground shadow-glow" : "text-muted-foreground hover:text-foreground hover:bg-secondary"}`}>
            <item.icon className="w-4 h-4" />
            <span className="text-sm font-medium">{item.label}</span>
          </Link>)}
        </div>

        {/* User Menu */}
        <div className="hidden md:flex items-center space-x-4">
          <Button variant="ghost" size="icon">
            <Bell className="w-4 h-4" />
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon">
                <User className="w-4 h-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem disabled>
                <User className="w-4 h-4 mr-2" />
                {currentUser?.name || currentUser?.email || "User"}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <Link to="/settings">
                  <Settings className="w-4 h-4 mr-2" />
                  Settings
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={logout}>
                <LogOut className="w-4 h-4 mr-2" />
                Sign Out
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Mobile Menu Button */}
        <Button variant="ghost" size="icon" className="md:hidden" onClick={() => setIsOpen(!isOpen)}>
          {isOpen ? <X className="w-4 h-4" /> : <Menu className="w-4 h-4" />}
        </Button>
      </div>

      {/* Mobile Menu */}
      {isOpen && <div className="md:hidden py-4 border-t border-border">
        <div className="flex flex-col space-y-2">
          {navItems.map(item => <Link key={item.label} to={item.href} className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors ${location.pathname === item.href ? "bg-primary text-primary-foreground shadow-glow" : "text-muted-foreground hover:text-foreground hover:bg-secondary"}`} onClick={() => setIsOpen(false)}>
            <item.icon className="w-4 h-4" />
            <span className="text-sm font-medium">{item.label}</span>
          </Link>)}
        </div>
      </div>}
    </div>
  </nav>;
};
export default Navigation;