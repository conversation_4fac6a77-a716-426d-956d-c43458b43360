<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32">
  <defs>
    <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8b5cf6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="16" cy="16" r="15" fill="url(#grad)" stroke="#1e293b" stroke-width="2"/>
  
  <!-- API mesh pattern -->
  <g fill="white" stroke="white" stroke-width="0.5" opacity="0.9">
    <!-- Central node -->
    <circle cx="16" cy="16" r="2" fill="white"/>
    
    <!-- Connection lines -->
    <line x1="16" y1="16" x2="8" y2="10" stroke-width="1"/>
    <line x1="16" y1="16" x2="24" y2="10" stroke-width="1"/>
    <line x1="16" y1="16" x2="8" y2="22" stroke-width="1"/>
    <line x1="16" y1="16" x2="24" y2="22" stroke-width="1"/>
    
    <!-- Outer nodes -->
    <circle cx="8" cy="10" r="1.5" fill="white"/>
    <circle cx="24" cy="10" r="1.5" fill="white"/>
    <circle cx="8" cy="22" r="1.5" fill="white"/>
    <circle cx="24" cy="22" r="1.5" fill="white"/>
    
    <!-- Pulse effect rings -->
    <circle cx="16" cy="16" r="6" fill="none" stroke="white" stroke-width="1" opacity="0.6"/>
    <circle cx="16" cy="16" r="9" fill="none" stroke="white" stroke-width="0.5" opacity="0.3"/>
  </g>
</svg>
