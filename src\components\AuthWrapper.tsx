import { Authenticated, Unauthenticated, AuthLoading } from "@convex-dev/auth/react";
import SignInForm from "./SignInForm";
import { Loader2 } from "lucide-react";
import { useAuth } from "@/hooks/useAuth";
import { useEffect, useState } from "react";

interface AuthWrapperProps {
  children: React.ReactNode;
}

export default function AuthWrapper({ children }: AuthWrapperProps) {
  const { ensureSettings } = useAuth();
  const [settingsInitialized, setSettingsInitialized] = useState(false);

  useEffect(() => {
    let mounted = true;

    // Initialize settings when component mounts and user is authenticated
    const initializeSettings = async () => {
      try {
        await ensureSettings();
        if (mounted) {
          setSettingsInitialized(true);
        }
      } catch (error) {
        console.error("Failed to ensure user settings:", error);
        if (mounted) {
          setSettingsInitialized(true);
        }
      }
    };

    // Reset initialization state when component unmounts/remounts
    setSettingsInitialized(false);
    initializeSettings();

    return () => {
      mounted = false;
    };
  }, [ensureSettings]);

  return (
    <>
      <AuthLoading>
        <div className="min-h-screen flex items-center justify-center bg-background">
          <div className="text-center">
            <Loader2 className="w-8 h-8 animate-spin text-primary mx-auto mb-4" />
            <p className="text-muted-foreground">Loading...</p>
          </div>
        </div>
      </AuthLoading>
      <Unauthenticated>
        <SignInForm />
      </Unauthenticated>
      <Authenticated>
        {!settingsInitialized ? (
          <div className="min-h-screen flex items-center justify-center bg-background">
            <div className="text-center">
              <Loader2 className="w-8 h-8 animate-spin text-primary mx-auto mb-4" />
              <p className="text-muted-foreground">Setting up your account...</p>
            </div>
          </div>
        ) : (
          children
        )}
      </Authenticated>
    </>
  );
}
