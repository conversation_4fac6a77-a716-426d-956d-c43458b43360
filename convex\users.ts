import { v } from 'convex/values';
import { query, mutation } from './_generated/server';
import { getAuthUserId } from '@convex-dev/auth/server';

// Get current authenticated user
export const getCurrentUser = query({
	args: {},
	handler: async (ctx) => {
		const userId = await getAuthUserId(ctx);
		if (!userId) {
			return null;
		}

		const user = await ctx.db.get(userId);
		if (!user) {
			return null;
		}

		// Return user data without sensitive information
		return {
			_id: user._id,
			name: user.name,
			email: user.email,
		};
	},
});

// Get user settings
export const getUserSettings = query({
	args: {},
	handler: async (ctx) => {
		const userId = await getAuthUserId(ctx);
		if (!userId) {
			return null;
		}

		const settings = await ctx.db
			.query('userSettings')
			.withIndex('by_user', (q) => q.eq('userId', userId))
			.unique();

		return (
			settings || {
				emailNotifications: true,
				pushNotifications: true,
				alertThresholds: {
					errorRateThreshold: 5.0,
					latencyThreshold: 1000,
				},
				defaultTimeout: 30000,
				defaultRetries: 3,
			}
		);
	},
});

// Ensure user settings exist (create if they don't)
export const ensureUserSettings = mutation({
	args: {},
	handler: async (ctx) => {
		const userId = await getAuthUserId(ctx);
		if (!userId) {
			throw new Error('Not authenticated');
		}

		const existingSettings = await ctx.db
			.query('userSettings')
			.withIndex('by_user', (q) => q.eq('userId', userId))
			.unique();

		if (!existingSettings) {
			const newSettings = {
				userId,
				emailNotifications: true,
				pushNotifications: true,
				alertThresholds: {
					errorRateThreshold: 5.0,
					latencyThreshold: 1000,
				},
				defaultTimeout: 30000,
				defaultRetries: 3,
				updatedAt: Date.now(),
			};

			await ctx.db.insert('userSettings', newSettings);
			return newSettings;
		}

		return existingSettings;
	},
});

// Update user settings
export const updateUserSettings = mutation({
	args: {
		emailNotifications: v.optional(v.boolean()),
		pushNotifications: v.optional(v.boolean()),
		alertThresholds: v.optional(
			v.object({
				errorRateThreshold: v.number(),
				latencyThreshold: v.number(),
			})
		),
		defaultTimeout: v.optional(v.number()),
		defaultRetries: v.optional(v.number()),
	},
	handler: async (ctx, args) => {
		const userId = await getAuthUserId(ctx);
		if (!userId) {
			throw new Error('Not authenticated');
		}

		const settings = await ctx.db
			.query('userSettings')
			.withIndex('by_user', (q) => q.eq('userId', userId))
			.unique();

		if (!settings) {
			// Create new settings
			await ctx.db.insert('userSettings', {
				userId,
				emailNotifications: args.emailNotifications ?? true,
				pushNotifications: args.pushNotifications ?? true,
				alertThresholds: args.alertThresholds ?? {
					errorRateThreshold: 5.0,
					latencyThreshold: 1000,
				},
				defaultTimeout: args.defaultTimeout ?? 30000,
				defaultRetries: args.defaultRetries ?? 3,
				updatedAt: Date.now(),
			});
		} else {
			// Update existing settings
			await ctx.db.patch(settings._id, {
				...(args.emailNotifications !== undefined && {
					emailNotifications: args.emailNotifications,
				}),
				...(args.pushNotifications !== undefined && {
					pushNotifications: args.pushNotifications,
				}),
				...(args.alertThresholds !== undefined && {
					alertThresholds: args.alertThresholds,
				}),
				...(args.defaultTimeout !== undefined && {
					defaultTimeout: args.defaultTimeout,
				}),
				...(args.defaultRetries !== undefined && {
					defaultRetries: args.defaultRetries,
				}),
				updatedAt: Date.now(),
			});
		}

		return true;
	},
});
