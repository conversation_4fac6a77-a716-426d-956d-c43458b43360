import { convexAuth } from '@convex-dev/auth/server';
import { Password } from '@convex-dev/auth/providers/Password';

export const { auth, signIn, signOut, store } = convexAuth({
	providers: [
		// Password authentication with email
		Password({
			profile(params) {
				return {
					email: params.email as string,
					name: (params.name as string) || (params.email as string),
				};
			},
		}),
	],
});
