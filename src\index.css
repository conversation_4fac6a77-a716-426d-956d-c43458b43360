@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* Dark professional theme for monitoring dashboard */
    --background: 222 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222 84% 6%;
    --card-foreground: 210 40% 98%;

    --popover: 222 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    /* Primary: Cyber blue */
    --primary: 200 98% 60%;
    --primary-foreground: 222 84% 4.9%;
    --primary-glow: 200 98% 70%;

    /* Secondary: Steel gray */
    --secondary: 217 32% 17%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217 32% 15%;
    --muted-foreground: 215 20% 65%;

    /* Accent: Electric purple */
    --accent: 280 100% 70%;
    --accent-foreground: 222 84% 4.9%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;

    /* Status colors for API monitoring */
    --success: 142 76% 36%;
    --success-foreground: 210 40% 98%;
    --warning: 38 92% 50%;
    --warning-foreground: 222 84% 4.9%;
    --error: 0 84% 60%;
    --error-foreground: 210 40% 98%;

    --border: 217 32% 17%;
    --input: 217 32% 17%;
    --ring: 200 98% 60%;

    /* Gradients for visual appeal */
    --gradient-primary: linear-gradient(135deg, hsl(200 98% 60%), hsl(280 100% 70%));
    --gradient-success: linear-gradient(135deg, hsl(142 76% 36%), hsl(142 76% 46%));
    --gradient-error: linear-gradient(135deg, hsl(0 84% 60%), hsl(0 84% 50%));
    --gradient-cyber: linear-gradient(135deg, hsl(200 98% 60%), hsl(280 100% 70%), hsl(200 98% 60%));

    /* Shadows */
    --shadow-glow: 0 0 20px hsl(200 98% 60% / 0.3);
    --shadow-success: 0 0 20px hsl(142 76% 36% / 0.3);
    --shadow-error: 0 0 20px hsl(0 84% 60% / 0.3);

    --radius: 0.75rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}