import { defineSchema, defineTable } from 'convex/server';
import { v } from 'convex/values';
import { authTables } from '@convex-dev/auth/server';

export default defineSchema({
	// Extend auth tables with our custom tables
	...authTables,

	// Users table for additional profile data (auth tables handle core auth)
	users: defineTable({
		name: v.string(),
		email: v.string(),
		role: v.union(v.literal('admin'), v.literal('user')),
		createdAt: v.number(),
		lastLoginAt: v.optional(v.number()),
		isActive: v.boolean(),
		settings: v.optional(
			v.object({
				theme: v.optional(v.string()),
				notifications: v.optional(v.boolean()),
				timezone: v.optional(v.string()),
			})
		),
	}).index('by_email', ['email']),

	// API Providers being monitored
	providers: defineTable({
		name: v.string(),
		type: v.union(
			v.literal('payment'),
			v.literal('sms'),
			v.literal('email'),
			v.literal('maps'),
			v.literal('ai'),
			v.literal('storage'),
			v.literal('auth'),
			v.literal('video'),
			v.literal('realtime'),
			v.literal('analytics'),
			v.literal('monitoring')
		),
		endpoint: v.string(),
		isHealthy: v.boolean(),
		latency: v.number(),
		errorRate: v.number(),
		priority: v.number(),
		isPrimary: v.optional(v.boolean()),
		lastCheck: v.optional(v.number()),
		createdBy: v.id('users'),
		createdAt: v.number(),
		updatedAt: v.number(),
		configuration: v.optional(
			v.object({
				timeout: v.optional(v.number()),
				retries: v.optional(v.number()),
				headers: v.optional(v.any()),
			})
		),
	})
		.index('by_type', ['type'])
		.index('by_health', ['isHealthy'])
		.index('by_priority', ['priority'])
		.index('by_created_by', ['createdBy']),

	// Health check results
	healthChecks: defineTable({
		providerId: v.id('providers'),
		timestamp: v.number(),
		status: v.union(
			v.literal('healthy'),
			v.literal('degraded'),
			v.literal('down')
		),
		latency: v.number(),
		errorRate: v.number(),
		details: v.optional(v.string()),
		responseCode: v.optional(v.number()),
		errorMessage: v.optional(v.string()),
	})
		.index('by_provider', ['providerId'])
		.index('by_timestamp', ['timestamp'])
		.index('by_status', ['status']),

	// API request logs
	requests: defineTable({
		providerId: v.id('providers'),
		endpoint: v.string(),
		method: v.string(),
		timestamp: v.number(),
		duration: v.number(),
		success: v.boolean(),
		errorCode: v.optional(v.string()),
		responseSize: v.optional(v.number()),
		userAgent: v.optional(v.string()),
		ipAddress: v.optional(v.string()),
	})
		.index('by_provider', ['providerId'])
		.index('by_timestamp', ['timestamp'])
		.index('by_success', ['success']),

	// System alerts and notifications
	alerts: defineTable({
		title: v.string(),
		message: v.string(),
		type: v.union(
			v.literal('error'),
			v.literal('warning'),
			v.literal('info'),
			v.literal('success')
		),
		severity: v.union(
			v.literal('low'),
			v.literal('medium'),
			v.literal('high'),
			v.literal('critical')
		),
		providerId: v.optional(v.id('providers')),
		isRead: v.boolean(),
		isResolved: v.boolean(),
		createdAt: v.number(),
		resolvedAt: v.optional(v.number()),
		resolvedBy: v.optional(v.id('users')),
		metadata: v.optional(v.any()),
	})
		.index('by_type', ['type'])
		.index('by_severity', ['severity'])
		.index('by_provider', ['providerId'])
		.index('by_read', ['isRead'])
		.index('by_resolved', ['isResolved'])
		.index('by_created_at', ['createdAt']),

	// Circuit breaker states
	circuitBreakers: defineTable({
		providerId: v.id('providers'),
		state: v.union(
			v.literal('closed'),
			v.literal('open'),
			v.literal('half-open')
		),
		failureCount: v.number(),
		lastFailureTime: v.optional(v.number()),
		nextAttemptTime: v.optional(v.number()),
		successCount: v.number(),
		threshold: v.number(),
		timeout: v.number(),
		createdAt: v.number(),
		updatedAt: v.number(),
	})
		.index('by_provider', ['providerId'])
		.index('by_state', ['state']),

	// Performance metrics
	metrics: defineTable({
		providerId: v.optional(v.id('providers')),
		metricType: v.union(
			v.literal('latency'),
			v.literal('throughput'),
			v.literal('error_rate'),
			v.literal('uptime'),
			v.literal('cpu_usage'),
			v.literal('memory_usage')
		),
		value: v.number(),
		timestamp: v.number(),
		tags: v.optional(v.any()),
	})
		.index('by_provider', ['providerId'])
		.index('by_type', ['metricType'])
		.index('by_timestamp', ['timestamp']),

	// User settings and preferences
	userSettings: defineTable({
		userId: v.id('users'),
		emailNotifications: v.boolean(),
		pushNotifications: v.boolean(),
		alertThresholds: v.object({
			errorRateThreshold: v.number(),
			latencyThreshold: v.number(),
		}),
		defaultTimeout: v.number(),
		defaultRetries: v.number(),
		updatedAt: v.number(),
	}).index('by_user', ['userId']),

	// API keys and tokens for external services
	apiKeys: defineTable({
		userId: v.id('users'),
		providerId: v.optional(v.id('providers')),
		name: v.string(),
		keyHash: v.string(), // Store hashed version for security
		lastUsed: v.optional(v.number()),
		isActive: v.boolean(),
		permissions: v.array(v.string()),
		expiresAt: v.optional(v.number()),
		createdAt: v.number(),
	})
		.index('by_user', ['userId'])
		.index('by_provider', ['providerId'])
		.index('by_active', ['isActive']),
});
